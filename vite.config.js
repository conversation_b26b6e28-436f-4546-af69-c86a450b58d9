// import { sentryVitePlugin } from "@sentry/vite-plugin";
import { fileURLToPath, URL } from 'node:url'

import { defineConfig } from 'vite'
import vue from '@vitejs/plugin-vue'

// https://vitejs.dev/config/
export default defineConfig({
  plugins: [vue()],
  // , sentryVitePlugin({
  //   org: "sentry",
  //   project: "hrv-fe",
  //   url: "https://sentry.airdoc.com/"
  // })
  resolve: {
    alias: {
      '@': fileURLToPath(new URL('./src', import.meta.url))
    }
  },

  server: {
    proxy: {
      '/speedtest': {
        target: 'https://ada-res.airdoc.com',
        changeOrigin: true,
        rewrite: path => path.replace(/^\/speedtest/, '')
      },
      // Handle material requests with any prefix
      '/material': {
        target: 'http://localhost:5173',
        changeOrigin: true,
        rewrite: (path) => {
          // Extract the filename from the path (e.g., /material/1/acorns-1.webp -> acorns-1.webp)
          const filename = path.split('/').pop();

          // Convert webp to jpg and handle naming conventions
          let actualFilename = filename;
          if (filename.endsWith('.webp')) {
            actualFilename = filename.replace('.webp', '.jpg');
          }

          // Convert hyphenated names to space-separated names with proper capitalization
          // e.g., acorns-1 -> Acorns 1
          actualFilename = actualFilename.replace(/^([a-z])/, (match) => match.toUpperCase())
                                       .replace(/-(\d+)/, ' $1');

          console.log(`Material request: ${path} -> /image_new/${actualFilename}`);
          return `/image_new/${actualFilename}`;
        }
      }
    }
  },

  build: {
    sourcemap: true,
    rollupOptions: {
      output: {
        manualChunks(id) {
          if (id.includes('node_modules')) {
            // 例如：vue、element-plus、echarts等会被单独分包
            return id.toString().split('node_modules/')[1].split('/')[0].toString();
          }
        }
      }
    }
  }
})